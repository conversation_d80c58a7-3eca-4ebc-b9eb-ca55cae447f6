<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\Product;
use App\Models\Service;
use App\Models\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class OrderSeeder extends Seeder
{
    public function run(): void
    {
        // Get client user
        $client = User::where('email', '<EMAIL>')->first();
        
        if (!$client) {
            $this->command->info('Client user not found. Please run UserSeeder first.');
            return;
        }
        
        // Get products
        $products = Product::all();
        
        if ($products->isEmpty()) {
            $this->command->info('No products found. Please run ProductSeeder first.');
            return;
        }
        
        // Create product orders
        foreach ($products->take(3) as $index => $product) {
            $status = ['Completed', 'Processing', 'Pending'][$index % 3];

            Order::create([
                'user_id' => $client->id,
                'product_id' => $product->id,
                'service_id' => null,
                'order_type' => 'product',
                'price' => $product->price,
                'status' => $status,
                'transaction_id' => 'TRX' . strtoupper(substr(md5(rand()), 0, 10)),
                'payment_method' => 'paypal',
                'created_at' => Carbon::now()->subDays(rand(1, 30)),
            ]);
        }

        // Create service orders
        $services = Service::all();

        if (!$services->isEmpty()) {
            foreach ($services->take(2) as $index => $service) {
                $status = ['Processing', 'Pending'][$index % 2];

                Order::create([
                    'user_id' => $client->id,
                    'product_id' => null,
                    'service_id' => $service->id,
                    'order_type' => 'service',
                    'price' => $service->price,
                    'status' => $status,
                    'transaction_id' => 'TRX' . strtoupper(substr(md5(rand()), 0, 10)),
                    'payment_method' => 'paypal',
                    'auto_complete_date' => Carbon::now()->addDays(30),
                    'created_at' => Carbon::now()->subDays(rand(1, 15)),
                ]);
            }
        }
    }
}
