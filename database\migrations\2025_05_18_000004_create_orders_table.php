<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->uuid('product_id')->nullable();
            $table->uuid('service_id')->nullable();
            $table->enum('order_type', ['product', 'service'])->default('product');
            $table->decimal('price', 10, 2);
            $table->enum('status', ['Pending', 'Processing', 'Completed', 'Cancelled', 'Refunded'])->default('Pending');
            $table->string('transaction_id')->nullable();

            // PayPal fields
            $table->string('payment_method')->default('paypal');
            $table->string('paypal_payment_id')->nullable();
            $table->string('paypal_payer_id')->nullable();
            $table->json('paypal_payment_details')->nullable();

            // Service management fields
            $table->timestamp('completed_at')->nullable();
            $table->text('cancellation_reason')->nullable();
            $table->timestamp('auto_complete_date')->nullable();

            $table->timestamps();

            // Foreign keys
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->foreign('service_id')->references('id')->on('services')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
