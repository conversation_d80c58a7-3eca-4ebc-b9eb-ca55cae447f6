<?php $__env->startSection('title', 'Orders'); ?>

<?php $__env->startSection('header', 'Orders'); ?>

<?php $__env->startSection('sidebar'); ?>
<div class="p-4">
    <a href="<?php echo e(route('home')); ?>" class="flex items-center space-x-2">
        <div class="relative h-8 w-8 overflow-hidden rounded">
            <div class="absolute inset-0 bg-[#710d17] flex items-center justify-center">
                <span class="text-[#fcefcc] font-bold text-lg">H</span>
            </div>
        </div>
        <span class="font-bold">Hermosart</span>
    </a>
</div>

<div class="mt-6 px-4">
    <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Main</h2>
    <nav class="mt-2 space-y-1">
        <a href="<?php echo e(route('dashboard.client')); ?>" class="flex items-center px-4 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('dashboard.client') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100'); ?>">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 <?php echo e(request()->routeIs('dashboard.client') ? 'text-white' : 'text-gray-400'); ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Dashboard
        </a>
        <a href="<?php echo e(route('dashboard.client.projects')); ?>" class="flex items-center px-4 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('dashboard.client.projects') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100'); ?>">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 <?php echo e(request()->routeIs('dashboard.client.projects') ? 'text-white' : 'text-gray-400'); ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Projects
        </a>
        <a href="<?php echo e(route('dashboard.client.messages')); ?>" class="flex items-center px-4 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('dashboard.client.messages') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100'); ?>">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 <?php echo e(request()->routeIs('dashboard.client.messages') ? 'text-white' : 'text-gray-400'); ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            Messages
        </a>
        <a href="<?php echo e(route('dashboard.client.favorites')); ?>" class="flex items-center px-4 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('dashboard.client.favorites') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100'); ?>">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 <?php echo e(request()->routeIs('dashboard.client.favorites') ? 'text-white' : 'text-gray-400'); ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            Favorites
        </a>
        <a href="<?php echo e(route('dashboard.client.orders')); ?>" class="flex items-center px-4 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('dashboard.client.orders') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100'); ?>">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 <?php echo e(request()->routeIs('dashboard.client.orders') ? 'text-white' : 'text-gray-400'); ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            Orders
        </a>
    </nav>
</div>

<div class="mt-6 px-4">
    <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Account</h2>
    <nav class="mt-2 space-y-1">
        <a href="<?php echo e(route('dashboard.client.profile')); ?>" class="flex items-center px-4 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('dashboard.client.profile') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100'); ?>">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 <?php echo e(request()->routeIs('dashboard.client.profile') ? 'text-white' : 'text-gray-400'); ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Profile
        </a>
        <a href="<?php echo e(route('dashboard.client.settings')); ?>" class="flex items-center px-4 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('dashboard.client.settings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100'); ?>">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 <?php echo e(request()->routeIs('dashboard.client.settings') ? 'text-white' : 'text-gray-400'); ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Settings
        </a>
    </nav>
</div>

<div class="mt-6 px-4">
    <a href="<?php echo e(route('home')); ?>" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
        Back to Website
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('mobile_sidebar'); ?>
<div class="px-2 pt-2 pb-3 space-y-1">
    <a href="<?php echo e(route('dashboard.client')); ?>" class="block px-3 py-2 rounded-md text-base font-medium <?php echo e(request()->routeIs('dashboard.client') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100'); ?>">Dashboard</a>
    <a href="<?php echo e(route('dashboard.client.projects')); ?>" class="block px-3 py-2 rounded-md text-base font-medium <?php echo e(request()->routeIs('dashboard.client.projects') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100'); ?>">Projects</a>
    <a href="<?php echo e(route('dashboard.client.messages')); ?>" class="block px-3 py-2 rounded-md text-base font-medium <?php echo e(request()->routeIs('dashboard.client.messages') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100'); ?>">Messages</a>
    <a href="<?php echo e(route('dashboard.client.favorites')); ?>" class="block px-3 py-2 rounded-md text-base font-medium <?php echo e(request()->routeIs('dashboard.client.favorites') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100'); ?>">Favorites</a>
    <a href="<?php echo e(route('dashboard.client.orders')); ?>" class="block px-3 py-2 rounded-md text-base font-medium <?php echo e(request()->routeIs('dashboard.client.orders') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100'); ?>">Orders</a>
    <a href="<?php echo e(route('dashboard.client.profile')); ?>" class="block px-3 py-2 rounded-md text-base font-medium <?php echo e(request()->routeIs('dashboard.client.profile') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100'); ?>">Profile</a>
    <a href="<?php echo e(route('dashboard.client.settings')); ?>" class="block px-3 py-2 rounded-md text-base font-medium <?php echo e(request()->routeIs('dashboard.client.settings') ? 'bg-[#710d17] text-white' : 'text-gray-700 hover:bg-gray-100'); ?>">Settings</a>
    <a href="<?php echo e(route('home')); ?>" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">Back to Website</a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="mb-4">
    <a href="<?php echo e(route('dashboard.client')); ?>" class="inline-flex items-center text-sm font-medium text-[#710d17] hover:text-[#9a2c39]">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back to Dashboard
    </a>
</div>

<div class="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
    <h2 class="text-2xl font-bold text-gray-900">My Orders</h2>
    <a href="<?php echo e(route('products')); ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
        <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
        </svg>
        Browse Products
    </a>
</div>

<!-- Desktop view (table) -->
<div class="hidden md:block bg-white shadow overflow-hidden rounded-lg">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order ID
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php $__empty_1 = true; $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        #<?php echo e(substr($order->id, 0, 8)); ?>

                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-md flex items-center justify-center overflow-hidden">
                                <?php if($order->product && $order->product->image): ?>
                                <img src="<?php echo e(asset($order->product->image)); ?>" alt="<?php echo e($order->product->name); ?>" class="h-10 w-10 rounded-md object-cover">
                                <?php else: ?>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <?php endif; ?>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php if($order->order_type === 'service' && $order->service): ?>
                                        <?php echo e($order->service->title); ?>

                                    <?php elseif($order->product): ?>
                                        <?php echo e($order->product->name); ?>

                                    <?php else: ?>
                                        Unknown Item
                                    <?php endif; ?>
                                </div>
                                <div class="text-sm text-gray-500">
                                    <?php echo e($order->order_type === 'service' ? 'Professional Service' : 'Digital Product'); ?>

                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        $<?php echo e(number_format($order->price, 2)); ?>

                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                            <?php if($order->status == 'Completed'): ?> bg-green-100 text-green-800
                            <?php elseif($order->status == 'Processing'): ?> bg-blue-100 text-blue-800
                            <?php elseif($order->status == 'Pending'): ?> bg-yellow-100 text-yellow-800
                            <?php else: ?> bg-red-100 text-red-800
                            <?php endif; ?>
                        ">
                            <?php echo e($order->status); ?>

                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <?php echo e($order->created_at->format('M d, Y')); ?>

                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <a href="<?php echo e(route('dashboard.client.orders.show', $order->id)); ?>" class="text-[#710d17] hover:text-[#9a2c39]">View</a>
                        <?php if($order->status == 'Completed'): ?>
                        <a href="<?php echo e(route('dashboard.client.orders.download', $order->id)); ?>" class="ml-3 text-[#710d17] hover:text-[#9a2c39]">Download</a>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="6" class="px-6 py-10 text-center text-sm text-gray-500">
                        <div class="flex flex-col items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No orders yet</h3>
                            <p class="mt-1 text-sm text-gray-500">Get started by browsing our products.</p>
                            <div class="mt-6">
                                <a href="<?php echo e(route('products')); ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                                    Browse Products
                                </a>
                            </div>
                        </div>
                    </td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Mobile view (cards) -->
<div class="md:hidden space-y-4">
    <?php $__empty_1 = true; $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
    <div class="bg-white shadow overflow-hidden rounded-lg">
        <div class="px-4 py-4 border-b border-gray-100 flex justify-between items-center">
            <div>
                <span class="text-xs text-gray-500">Order ID</span>
                <p class="text-sm font-medium text-gray-900">#<?php echo e(substr($order->id, 0, 8)); ?></p>
            </div>
            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                <?php if($order->status == 'Completed'): ?> bg-green-100 text-green-800
                <?php elseif($order->status == 'Processing'): ?> bg-blue-100 text-blue-800
                <?php elseif($order->status == 'Pending'): ?> bg-yellow-100 text-yellow-800
                <?php else: ?> bg-red-100 text-red-800
                <?php endif; ?>
            ">
                <?php echo e($order->status); ?>

            </span>
        </div>

        <div class="px-4 py-3 border-b border-gray-100 flex items-center">
            <div class="flex-shrink-0 h-12 w-12 bg-gray-200 rounded-md flex items-center justify-center overflow-hidden">
                <?php if($order->product && $order->product->image): ?>
                <img src="<?php echo e(asset($order->product->image)); ?>" alt="<?php echo e($order->product->name); ?>" class="h-12 w-12 rounded-md object-cover">
                <?php else: ?>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <?php endif; ?>
            </div>
            <div class="ml-4">
                <div class="text-sm font-medium text-gray-900">
                    <?php if($order->order_type === 'service' && $order->service): ?>
                        <?php echo e($order->service->title); ?>

                    <?php elseif($order->product): ?>
                        <?php echo e($order->product->name); ?>

                    <?php else: ?>
                        Unknown Item
                    <?php endif; ?>
                </div>
                <div class="text-sm text-gray-500">
                    <?php echo e($order->order_type === 'service' ? 'Professional Service' : 'Digital Product'); ?> • $<?php echo e(number_format($order->price, 2)); ?>

                </div>
            </div>
        </div>

        <div class="px-4 py-3 border-b border-gray-100 grid grid-cols-2 gap-3">
            <div>
                <span class="text-xs text-gray-500">Date</span>
                <p class="text-sm text-gray-900"><?php echo e($order->created_at->format('M d, Y')); ?></p>
            </div>
            <div>
                <span class="text-xs text-gray-500">Payment</span>
                <p class="text-sm text-gray-900"><?php echo e($order->payment_method ?? 'Credit Card'); ?></p>
            </div>
        </div>

        <div class="px-4 py-3 flex justify-between items-center">
            <a href="<?php echo e(route('dashboard.client.orders.show', $order->id)); ?>" class="inline-flex items-center px-3 py-1.5 border border-[#710d17] rounded-md text-sm font-medium text-[#710d17] bg-white hover:bg-[#fcefcc]/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                View Details
            </a>

            <?php if($order->status == 'Completed'): ?>
            <a href="<?php echo e(route('dashboard.client.orders.download', $order->id)); ?>" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download
            </a>
            <?php endif; ?>
        </div>
    </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
    <div class="bg-white overflow-hidden shadow rounded-lg p-8 text-center">
        <div class="max-w-md mx-auto">
            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            <h3 class="mt-4 text-lg font-medium text-gray-900">No orders yet</h3>
            <p class="mt-2 text-sm text-gray-500">You haven't placed any orders yet. Browse our products and make your first purchase.</p>
            <div class="mt-6">
                <a href="<?php echo e(route('products')); ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                    <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                    Browse Products
                </a>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php if(count($orders) > 0): ?>
<div class="mt-6">
    <?php echo e($orders->links()); ?>

</div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\hermosart\resources\views/dashboard/client/orders.blade.php ENDPATH**/ ?>