@extends('layouts.app')

@section('title', 'Payment Successful - Hermosart')

@section('content')
    <div class="bg-white">
        <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Success Icon -->
            <div class="text-center mb-8">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                    <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                </div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Payment Successful!</h1>
                <p class="text-lg text-gray-600">Thank you for your purchase. Your order has been confirmed.</p>
            </div>

            <!-- Order Details -->
            <div class="bg-gray-50 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Order Details</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Product Info -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 mb-2">Product</h3>
                        <div class="flex items-center space-x-4">
                            @if ($order->product->image && Storage::exists('public/' . $order->product->image))
                                <img src="{{ asset($order->product->image) }}" alt="{{ $order->product->name }}"
                                    class="w-16 h-16 object-cover rounded-lg">
                            @else
                                <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                    </svg>
                                </div>
                            @endif
                            <div>
                                <p class="font-medium text-gray-900">{{ $order->product->name }}</p>
                                <p class="text-sm text-gray-500">Digital Product</p>
                            </div>
                        </div>
                    </div>

                    <!-- Order Info -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 mb-2">Order Information</h3>
                        <dl class="space-y-1">
                            <div class="flex justify-between">
                                <dt class="text-sm text-gray-600">Order ID:</dt>
                                <dd class="text-sm font-medium text-gray-900">{{ substr($order->id, 0, 8) }}...</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm text-gray-600">Date:</dt>
                                <dd class="text-sm font-medium text-gray-900">{{ $order->created_at->format('M d, Y') }}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm text-gray-600">Payment Method:</dt>
                                <dd class="text-sm font-medium text-gray-900">PayPal</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm text-gray-600">Total:</dt>
                                <dd class="text-lg font-bold text-[#710d17]">${{ number_format($order->price, 2) }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h3 class="text-lg font-medium text-blue-900 mb-2">What's Next?</h3>
                <ul class="text-sm text-blue-800 space-y-1">
                    <li>• You can download your digital product from your orders page</li>
                    <li>• A confirmation email has been sent to your email address</li>
                    <li>• For support, please contact us through our support page</li>
                </ul>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('dashboard.client.orders') }}"
                    class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                    View My Orders
                </a>
                <a href="{{ route('products') }}"
                    class="inline-flex items-center justify-center px-6 py-3 border border-[#710d17] text-base font-medium rounded-md text-[#710d17] bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                    Continue Shopping
                </a>
            </div>
        </div>
    </div>
@endsection
