<?php $__env->startSection('title', 'Payment Error - Hermosart'); ?>

<?php $__env->startSection('content'); ?>
    <div class="bg-white">
        <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Error Icon -->
            <div class="text-center mb-8">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                    <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Payment Error</h1>
                <p class="text-lg text-gray-600">We encountered an issue processing your payment.</p>
            </div>

            <!-- Error Details -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
                <h2 class="text-lg font-medium text-red-900 mb-2">What happened?</h2>
                <p class="text-sm text-red-800 mb-4">
                    <?php if(session('error')): ?>
                        <?php echo e(session('error')); ?>

                    <?php else: ?>
                        There was an issue processing your payment. This could be due to various reasons such as insufficient funds, network issues, or payment gateway problems.
                    <?php endif; ?>
                </p>
                
                <h3 class="text-md font-medium text-red-900 mb-2">What can you do?</h3>
                <ul class="text-sm text-red-800 space-y-1">
                    <li>• Check your payment method and try again</li>
                    <li>• Ensure you have sufficient funds in your account</li>
                    <li>• Try using a different payment method</li>
                    <li>• Contact our support team if the issue persists</li>
                </ul>
            </div>

            <!-- Support Information -->
            <div class="bg-gray-50 rounded-lg p-6 mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-2">Need Help?</h3>
                <p class="text-sm text-gray-600 mb-4">
                    If you continue to experience issues, please don't hesitate to contact our support team. We're here to help!
                </p>
                <div class="flex items-center space-x-4 text-sm text-gray-600">
                    <div class="flex items-center">
                        <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <EMAIL>
                    </div>
                    <div class="flex items-center">
                        <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        24/7 Support Available
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="history.back()"
                    class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-[#710d17] hover:bg-[#9a2c39] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                    Try Again
                </button>
                <a href="<?php echo e(route('products')); ?>"
                    class="inline-flex items-center justify-center px-6 py-3 border border-[#710d17] text-base font-medium rounded-md text-[#710d17] bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#710d17]">
                    Browse Products
                </a>
                <a href="<?php echo e(route('home')); ?>"
                    class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    Go Home
                </a>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\hermosart\resources\views/payment/error.blade.php ENDPATH**/ ?>