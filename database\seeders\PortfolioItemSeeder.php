<?php

namespace Database\Seeders;

use App\Models\PortfolioItem;
use App\Models\User;
use App\Models\Professional;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class PortfolioItemSeeder extends Seeder
{
    public function run(): void
    {
        // Get professional users
        $professionalUser = User::where('email', '<EMAIL>')->first();
        $proSellerUser = User::where('email', '<EMAIL>')->first();

        if (!$professionalUser || !$proSellerUser) {
            $this->command->info('Required users not found. Please run UserSeeder first.');
            return;
        }

        // Get professional records
        $professional = Professional::where('user_id', $professionalUser->id)->first();
        $proSeller = Professional::where('user_id', $proSellerUser->id)->first();

        if (!$professional || !$proSeller) {
            $this->command->info('Required professional records not found. Please run ProfessionalSeeder first.');
            return;
        }

        // Create portfolio items for the first professional
        PortfolioItem::create([
            'professional_id' => $professional->id,
            'title' => 'Modern Logo Design',
            'description' => 'A sleek, modern logo design for a tech startup that specializes in AI solutions.',
            'image' => 'portfolio/logo-design-1.jpg',
            'category' => 'design',
            'client_name' => 'TechLaunch Inc.',
            'completion_date' => Carbon::now()->subMonths(2),
            'featured' => true,
        ]);

        PortfolioItem::create([
            'professional_id' => $professional->id,
            'title' => 'E-commerce Website Redesign',
            'description' => 'Complete redesign of an e-commerce platform to improve user experience and conversion rates.',
            'image' => 'portfolio/web-design-1.jpg',
            'category' => 'web',
            'client_name' => 'Fashion Forward',
            'completion_date' => Carbon::now()->subMonths(3),
            'featured' => true,
        ]);

        PortfolioItem::create([
            'professional_id' => $professional->id,
            'title' => 'Brand Identity Package',
            'description' => 'Complete brand identity including logo, color palette, typography, and brand guidelines.',
            'image' => 'portfolio/brand-identity-1.jpg',
            'category' => 'design',
            'client_name' => 'Green Earth Foundation',
            'completion_date' => Carbon::now()->subMonths(5),
            'featured' => false,
        ]);

        PortfolioItem::create([
            'professional_id' => $professional->id,
            'title' => 'Mobile App UI Design',
            'description' => 'User interface design for a fitness tracking mobile application.',
            'image' => 'portfolio/app-design-1.jpg',
            'category' => 'design',
            'client_name' => 'FitLife Gym',
            'completion_date' => Carbon::now()->subMonths(1),
            'featured' => false,
        ]);

        // Create portfolio items for the second professional
        PortfolioItem::create([
            'professional_id' => $proSeller->id,
            'title' => 'Data Visualization Dashboard',
            'description' => 'Interactive dashboard for visualizing complex data sets for a financial services company.',
            'image' => 'portfolio/data-viz-1.jpg',
            'category' => 'data',
            'client_name' => 'Global Finance Partners',
            'completion_date' => Carbon::now()->subMonths(2),
            'featured' => true,
        ]);

        PortfolioItem::create([
            'professional_id' => $proSeller->id,
            'title' => 'AI-Powered Chatbot',
            'description' => 'Development of an AI chatbot for customer service automation.',
            'image' => 'portfolio/ai-chatbot-1.jpg',
            'category' => 'ai',
            'client_name' => 'Customer Connect',
            'completion_date' => Carbon::now()->subMonths(4),
            'featured' => true,
        ]);

        PortfolioItem::create([
            'professional_id' => $proSeller->id,
            'title' => 'E-commerce Product Recommendation Engine',
            'description' => 'Machine learning algorithm for personalized product recommendations.',
            'image' => 'portfolio/ml-recommender-1.jpg',
            'category' => 'ai',
            'client_name' => 'ShopSmart',
            'completion_date' => Carbon::now()->subMonths(6),
            'featured' => false,
        ]);

        PortfolioItem::create([
            'professional_id' => $proSeller->id,
            'title' => 'Data Analysis for Market Research',
            'description' => 'Comprehensive data analysis and visualization for market research report.',
            'image' => 'portfolio/data-analysis-1.jpg',
            'category' => 'data',
            'client_name' => 'Market Insights Inc.',
            'completion_date' => Carbon::now()->subMonths(3),
            'featured' => false,
        ]);
    }
}
